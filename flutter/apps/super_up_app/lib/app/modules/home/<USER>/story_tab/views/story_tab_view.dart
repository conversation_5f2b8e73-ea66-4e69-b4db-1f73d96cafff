// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up/app/core/app_config/app_config_controller.dart';
import 'package:super_up/app/modules/home/<USER>/story_tab/views/widgets/story_widget.dart';
import 'package:super_up/app/modules/memory/views/memory_view.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../../../core/models/story/story_model.dart';
import '../../../../story/view/story_view.dart';
import '../controllers/story_tab_controller.dart';
import '../../../../live_stream/views/live_stream_options_view.dart';
import 'package:s_translation/generated/l10n.dart';

class StoryTabView extends StatefulWidget {
  const StoryTabView({super.key});

  @override
  State<StoryTabView> createState() => _StoryTabViewState();
}

class _StoryTabViewState extends State<StoryTabView> {
  late final StoryTabController controller;

  @override
  void initState() {
    super.initState();
    controller = GetIt.I.get<StoryTabController>();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      child: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          CupertinoSliverNavigationBar(
            largeTitle: Text(
              S.of(context).stories,
              style: context.cupertinoTextTheme.textStyle.copyWith(
                fontSize: 25,
                fontWeight: FontWeight.w600,
              ),
            ),
            middle: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                //SHOW APP LOGO
                Image.asset('assets/logo.png', width: 25, height: 25),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  "OrbitChat",
                  style: context.cupertinoTextTheme.textStyle.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    _navigateToLiveStream(context);
                  },
                  child: const Icon(
                    CupertinoIcons.video_camera,
                    size: 24,
                    color: CupertinoColors.systemGreen,
                  ),
                ),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    _navigateToMemories(context);
                  },
                  child: const Icon(
                    CupertinoIcons.memories,
                    size: 24,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
              ],
            ),
          )
        ],
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AdsBannerWidget(
                adsId: VPlatforms.isAndroid
                    ? SConstants.androidBannerAdsUnitId
                    : SConstants.iosBannerAdsUnitId,
                isEnableAds: VAppConfigController.appConfig.enableAds,
              ),

              /// MyStory
              ValueListenableBuilder<SLoadingState<StoryTabState>>(
                valueListenable: controller,
                builder: (_, value, __) {
                  return StoryWidget(
                    isMe: true,
                    toCreateStory: () {
                      controller.toCreateStory(context);
                    },
                    isLoading: value.data.isMyStoriesLoading,
                    onTap: (storyModel) {
                      if (storyModel.stories.isEmpty) return;
                      context.toPage(
                        StoryViewpage(
                          storyModel: storyModel,
                          onComplete: (current) {},
                          onDelete: () async {
                            await controller.getMyStoryFromApi();
                          },
                          onStoryViewed: controller.markStoryAsViewed,
                        ),
                      );
                    },
                    storyModel: value.data.myStories,
                    onLongTap: (UserStoryModel storyModel) {},
                  );
                },
              ),

              Expanded(
                child: ValueListenableBuilder<SLoadingState<StoryTabState>>(
                  valueListenable: controller,
                  builder: (_, value, __) {
                    return VAsyncWidgetsBuilder(
                      loadingState: value.loadingState,
                      onRefresh: controller.getStories,
                      successWidget: () {
                        final unviewedStories = value.data.unviewedStories;
                        final viewedStories =
                            value.data.completelyViewedStories;

                        return ListView(
                          children: [
                            // Recent Updates Section
                            if (unviewedStories.isNotEmpty) ...[
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: Text(
                                  S.of(context).recentUpdate,
                                  style: context.cupertinoTextTheme.textStyle
                                      .copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: CupertinoColors.black,
                                  ),
                                ),
                              ),
                              ...unviewedStories
                                  .map((storyModel) => StoryWidget(
                                        isMe: false,
                                        isViewed: false,
                                        onLongTap: (storyModel) {},
                                        onTap: (storyModel) {
                                          if (storyModel.stories.isEmpty)
                                            return;
                                          context.toPage(
                                            StoryViewpage(
                                              storyModel: storyModel,
                                              onComplete: _onStoryComplete,
                                              onDelete: null,
                                              onStoryViewed:
                                                  controller.markStoryAsViewed,
                                            ),
                                          );
                                        },
                                        storyModel: storyModel,
                                      ))
                                  .toList(),
                            ],

                            // Viewed Updates Section
                            if (viewedStories.isNotEmpty) ...[
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: Text(
                                  "Viewed updates",
                                  style: context.cupertinoTextTheme.textStyle
                                      .copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: CupertinoColors.black,
                                  ),
                                ),
                              ),
                              ...viewedStories
                                  .map((storyModel) => StoryWidget(
                                        isMe: false,
                                        isViewed: true,
                                        onLongTap: (storyModel) {},
                                        onTap: (storyModel) {
                                          if (storyModel.stories.isEmpty)
                                            return;
                                          context.toPage(
                                            StoryViewpage(
                                              storyModel: storyModel,
                                              onComplete: _onStoryComplete,
                                              onDelete: null,
                                              onStoryViewed:
                                                  controller.markStoryAsViewed,
                                            ),
                                          );
                                        },
                                        storyModel: storyModel,
                                      ))
                                  .toList(),
                            ],

                            // Empty state
                            if (unviewedStories.isEmpty &&
                                viewedStories.isEmpty)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(32.0),
                                  child: Text(
                                    "No stories available",
                                    style: context.cupertinoTextTheme.textStyle
                                        .copyWith(
                                      color: CupertinoColors.systemGrey,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future _onStoryComplete(UserStoryModel current) async {
    final index = controller.data.allStories.indexOf(current);
    if (index == -1) return;

    // Check if there's a next story to navigate to
    if (index + 1 < controller.data.allStories.length) {
      context.toPage(
        StoryViewpage(
          storyModel: controller.data.allStories[index + 1],
          onComplete: _onStoryComplete,
          onDelete: null,
          onStoryViewed: controller.markStoryAsViewed,
        ),
      );
    }
    // If it's the last story, just return (story viewer will close automatically)
  }

  void _navigateToMemories(BuildContext context) {
    context.toPage(const MemoryView());
  }

  void _navigateToLiveStream(BuildContext context) {
    context.toPage(const LiveStreamOptionsView());
  }
}
